import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  LayersIcon,
  SettingsIcon,
  ChevronRightIcon,
  HelpCircle,
  MessageSquarePlus,
  Navigation,
  Zap,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { MagicCard } from "@/components/magicui/magic-card";
import { cn } from "@/lib/utils";

interface HelpMenuBarProps {
  setInput: (input: string) => void;
  hasMessages: boolean;
}

export const HelpMenuBar = ({ setInput }: HelpMenuBarProps) => {
  const [activePopover, setActivePopover] = useState<string | null>(null);
  const router = useRouter();

  const menuItems = [
    {
      id: "help",
      icon: <HelpCircle className="w-4 h-4" />,
      title: "도움말",
      tooltip: "전체 기능 안내",
      color: "text-blue-600",
      bgColor: "hover:bg-blue-50",
      features: [
        "🗺️ 장소 검색 및 이동: 주소검색, 현재위치확인, 길찾기",
        "⚙️ 지도 제어: 상하좌우 이동, 줌레벨 변경, 배경지도 변경",
        "📚 레이어 제어: 레이어 목록 조회, 상세 조회, 속성정보 조회, 스타일 변경, 레이어 제거",
        "📊 데이터 분석: 밀도 분석, 속성 필터링"
      ]
    },
    {
      id: "search",
      icon: <Navigation className="w-4 h-4" />,
      title: "장소 검색 및 이동",
      tooltip: "장소 검색 및 이동",
      color: "text-green-600",
      bgColor: "hover:bg-green-50",
      tools: ["주소검색", "현재위치확인", "길찾기"],
      examples: [
        { label: "위치 이동", command: "웨이버스로 이동해줘" },
        { label: "길찾기", command: "웨이버스에서 평촌역까지 얼마나 걸려?" },
        { label: "현재위치에서 길찾기", command: "내 위치에서 구로디지털단지역까지 얼마나걸려?" },
      ]
    },
    {
      id: "control",
      icon: <SettingsIcon className="w-4 h-4" />,
      title: "지도 제어",
      tooltip: "지도 제어",
      color: "text-orange-600",
      bgColor: "hover:bg-orange-50",
      tools: ["상하좌우 이동", "줌레벨 변경", "배경지도 변경"],
      examples: [
        { label: "지도 확대", command: "지도를 확대해줘" },
        { label: "지도 축소", command: "지도를 축소해줘" },
        { label: "위쪽으로 500m 이동", command: "위쪽으로 500m 이동해줘" },
        { label: "항공지도로 변경", command: "배경지도를 항공지도로 변경해줘" }
      ]
    },
    {
      id: "layers",
      icon: <LayersIcon className="w-4 h-4" />,
      title: "레이어 제어",
      tooltip: "레이어 제어",
      color: "text-purple-600",
      bgColor: "hover:bg-purple-50",
      tools: ["레이어 목록 조회", "상세 조회", "속성정보 조회", "스타일 변경", "유형별 스타일 변경", "레이어 제거"],
      examples: [
        { label: "레이어 추가", command: "택지개발사업 레이어를 추가해줘" },
        { label: "단일 스타일 설정", command: "백년가게를 노란색 별모양으로 보여줄래?" },
        { label: "유형별 스타일 설정", command: "서울에 있는 약국만 빨간색으로 표시해줘" },
        { label: "유형별 스타일 설정", command: "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?" },
      ]
    },
    {
      id: "analysis",
      icon: <Zap className="w-4 h-4" />,
      title: "데이터 분석",
      tooltip: "데이터 분석",
      color: "text-red-600",
      bgColor: "hover:bg-red-50",
      tools: ["밀도 분석", "속성 필터링"],
      examples: [
        { label: "노후화 건물 분석", command: "서울의 노후화된 건물을 보여줘" },
        { label: "레이어 밀도 분석", command: "AI 발생농가 지역의 밀집도를 분석해줘" },
      ]
    },
  ];

  const handleExampleClick = (command: string) => {
    setInput(command);
    setActivePopover(null);
  };

  const handleNewChat = () => {
    // 새 대화 시작 로직 (현재는 페이지 새로고침으로 구현)
    router.push('/');
    router.refresh();
  };

  // 메시지가 없을 때도 표시 (Overview와 함께 표시)
  return (
    <div className="flex items-center justify-between px-4 py-2 border-b border-border/80 bg-gradient-to-r from-secondary/45 to-secondary/10">
      {/* 왼쪽: 기능 아이콘들 */}
      <div className="flex items-center gap-2">
        {menuItems.map((item) => (
          <Popover
            key={item.id}
            open={activePopover === item.id}
            onOpenChange={(open) => setActivePopover(open ? item.id : null)}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <PopoverTrigger asChild>
                  <Button
                    variant="secondary"
                    size="sm"
                    className={cn(
                      "h-8 w-8 p-0 transition-all duration-200 border border-border/40 rounded-md",
                      "hover:border-border/60 hover:shadow-sm",
                      item.bgColor,
                      activePopover === item.id
                        ? `${item.color} bg-opacity-20 border-current shadow-sm`
                        : `${item.color} hover:${item.color}`
                    )}
                  >
                    {item.icon}
                  </Button>
                </PopoverTrigger>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="text-xs">
                {item.tooltip}
              </TooltipContent>
            </Tooltip>
            <PopoverContent
              className={cn(
                "p-0 border border-border/40 shadow-lg",
                item.id === "help" ? "w-[480px]" : "w-80 bg-gradient-to-b from-background/95 to-background/98 backdrop-blur-lg"
              )}
              align="start"
              side="bottom"
              sideOffset={8}
            >
              {/* 도움말 아이콘인 경우 MagicCard로 래핑 */}
              {item.id === "help" ? (
                <MagicCard
                  className="p-6 bg-gradient-to-br from-background via-background/95 to-background/90"
                  gradientSize={120}
                  gradientColor={"#3b82f6"}
                  gradientOpacity={0.05}
                  gradientFrom={"#3b82f6"}
                  gradientTo={"#6366f1"}
                >
                  {/* 헤더 */}
                  <div className="flex items-center gap-3 mb-3">
                    <div>
                      <h4 className="font-bold text-lg text-foreground">
                        {item.title}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        말로 만드는 지도 - 지원 기능 안내
                      </p>
                    </div>
                  </div>

                  {/* 전체 기능 안내 */}
                  {item.features && (
                    <div className="space-y-4">
                      <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg p-4">
                        <h5 className="font-semibold text-sm text-foreground mb-3 flex items-center gap-2">
                          <span className="w-2 h-2 bg-primary rounded-full"></span>
                          현재 지원하는 주요 기능들
                        </h5>
                        <div className="grid gap-3">
                          {item.features.map((feature, index) => (
                            <div key={index} className="flex items-start gap-3 p-3 bg-background/50 rounded-md border border-border/20">
                              <span className="text-lg mt-0.5">{feature.split(':')[0]}</span>
                              <div>
                                <p className="font-medium text-sm text-foreground">
                                  {feature.split(':')[1]?.split(' ')[1]}
                                </p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {feature.split(':')[1]?.substring(feature.split(':')[1].indexOf(' ') + 1)}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="text-center pt-2">
                        <p className="text-xs text-muted-foreground">
                          각 기능 아이콘을 클릭하면 상세한 사용 예시를 확인할 수 있습니다
                        </p>
                      </div>
                    </div>
                  )}
                </MagicCard>
              ) : (
                /* 일반 기능 아이콘인 경우 기존 스타일 */
                <div className="p-4 space-y-3">
                  {/* 헤더 */}
                  <div className="border-b border-border/40 pb-3">
                    <div className="flex items-center gap-2">
                      <div className={item.color}>
                        {item.icon}
                      </div>
                      <h4 className="font-semibold text-sm text-foreground">
                        {item.title}
                      </h4>
                    </div>

                    {item.tools && (
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground/80 mb-1">지원 도구:</p>
                        <p className="text-xs text-foreground/80">
                          {item.tools.join(", ")}
                        </p>
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground/80 mt-2">
                      예시를 클릭하면 자동으로 입력됩니다
                    </p>
                  </div>

                  {/* 예시 목록 */}
                  {item.examples && (
                    <div className="space-y-1 max-h-60 overflow-y-auto styled-scrollbar">
                      {item.examples.map((example, exampleIndex) => (
                        <Button
                          key={exampleIndex}
                          variant="ghost"
                          className="w-full group justify-between relative overflow-hidden rounded-md px-2 py-2 h-auto"
                          onClick={() => handleExampleClick(example.command)}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                          <div className="relative flex flex-col items-start gap-0.5 text-left flex-1">
                            <span className="text-xs font-medium text-foreground/90 group-hover:text-primary transition-colors">
                              {example.label}
                            </span>
                            <span className="text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors line-clamp-2">
                              {example.command}
                            </span>
                          </div>
                          <div className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <ChevronRightIcon className="w-3 h-3 text-primary" />
                          </div>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </PopoverContent>
          </Popover>
        ))}
      </div>

      {/* 오른쪽: 새 대화 버튼 */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="se"
            size="sm"
            className={cn(
              "h-8 w-8 p-0 transition-all duration-200 border border-border/40 rounded-md",
              "text-muted-foreground hover:text-foreground hover:bg-secondary/50 hover:border-border/60 hover:shadow-sm"
            )}
            onClick={handleNewChat}
          >
            <MessageSquarePlus className="w-4 h-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="text-xs">
          새 대화 시작
        </TooltipContent>
      </Tooltip>
    </div>
  );
};
